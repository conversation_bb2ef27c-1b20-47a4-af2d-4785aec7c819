import 'package:flutter/material.dart';

const fontFamily = 'Poppins';

// Padding
const primaryLayoutPadding = 22.0;
const defaultPadding = 16.0;

//margin
const webLayoutmargin =
    40.0; // wholecontent will be dispalyed within this boundary
const defaultMargin = 16.0;

// Screen Sizes
const smallMobileBreakpoint = 500;
const mobileBreakpoint = 800;
const tabletBreakpoint = 1200;
const desktopBreakpoint = 1400;

const sideDrawerBreakpoint = 1000;
const commissionCardBreakPoint = 1100;

//Broker screen dimensions
// Table dimensions.
const double headingRowHeight = 24.0;
const double tableDividerThickness = 1.0;
const double tableBorderWidth = 1.0;
const double dataTextSize = 14.0;
const double headerTextSize = 14.0;
const double paginationTextSize = 14.0;
const double paginationTextSizeMobile = 12.0;
const double emptyStateTextSize = 16.0;
const double emptyStatePadding = 48.0;
const double dataTextHeight = 1.0;
const double dataTextLetterSpacing = -0.14;
const double defaultColumnWidth = 500.0;
const double sortIconSize = 16.0;
const double userIconSize = 30.0;
const double userIconImageSize = 30.0;
const double searchIconSize = 8;

// Padding and spacing constants
const double cellPaddingHorizontalTablet = 4.0;
const double cellPaddingHorizontalDesktop = 8.0;
const double actionButtonSpacing = 8.0;
const double paginationHorizontalPaddingTablet = 4.0;
const double paginationHorizontalPaddingDesktop = 16.0;
const double paginationVerticalPaddingTablet = 8.0;
const double paginationVerticalPaddingDesktop = 60.0;
const double paginationTopPadding = 10.0;
const double paginationControlsSpacing = 8.0;
const double paginationButtonMarginMobile = 1.0;
const double paginationButtonMarginDesktop = 2.0;
const double paginationButtonSizeMobile = 28.0;
const double paginationButtonSizeDesktop = 32.0;
const double paginationButtonBorderRadius = 4.0;

// New pagination design constants
const double paginationArrowIconSize = 16.0;
const double paginationButtonMinSize = 32.0;
const double paginationButtonSpacing = 4.0;
const int maxVisiblePagesDesktop = 7;
const int maxVisiblePagesMobile = 5;

// Exact pagination button design constraints
const double paginationButtonWidth = 23.71;
const double paginationButtonHeight = 24.0;
const double paginationButtonPaddingTop = 6.0;
const double paginationButtonPaddingRight = 10.0;
const double paginationButtonPaddingBottom = 6.0;
const double paginationButtonPaddingLeft = 10.0;
const double paginationButtonGap = 10.0;

// Screen breakpoints
const double brokerTabletBreakpoint = 1000.0;
const double brokerMobileBreakpoint = 600.0;

// Filter and search UI constants
const double filterButtonHeight = 40.0;
const double filterButtonPaddingHorizontal = 12.0;
const double filterButtonPaddingVertical = 8.0;
const double filterButtonBorderRadius = 6.0;
const double filterIconSize = 16.0;
const double filterIconSpacing = 6.0;
const double searchFieldBorderRadius = 18.0;
const double searchIconPaddingLeft = 6.0;
const double searchIconPaddingVertical = 6.0;
const double searchContentPaddingHorizontal = 16.0;
const double searchContentPaddingVertical = 12.0;

// Filter and search text styling
const double filterTextSize = 14.0;
const double searchTextSize = 14.0;
const double searchHintTextSize = 14.0;
const FontWeight filterTextWeight = FontWeight.w400;
const FontWeight searchTextWeight = FontWeight.w400;
// Broker table specific constants
const double brokerNameSpacing = 8.0;
const double addressMaxHeight = 40.0;
const double addressLineHeight = 1.2;
const int addressMaxLines = 2;
const double actionIconContainerSize = 28.0;
const double titleIconSize = 23.0;
const double mobileScreenPadding = 16.0;
const int snackBarDurationSeconds = 2;
const double columnSpacing = 16.0;
const double horizontalMargin = 24.0;
const double dataRowMinHeight = 48.0;
const double dataRowMaxHeight = 52.0;
const int defaultItemsPerPage = 10;

// Action column specific constants
const double actionColumnPadding = 8.0;
const double actionIconSize = 20.0;

// Search field constraints
const double searchFieldMinWidth = 180.0;
const double searchFieldMaxWidth = 280.0;
const FontWeight brokerNameTextWeight = FontWeight.w500;
const double brokerNameTextSize = 14.0;
const double brokerNameLineHeight = 1.0;
const double brokerNameLetterSpacing = -0.14;
// Column width constants
const double actionsColumnWidthTablet = 100.0;
const double actionsColumnWidthDesktop = 120.0;
const double brokerNameColumnWidthTablet = 150.0;
const double brokerNameColumnWidthDesktop = 180.0;
const double contactsColumnWidthTablet = 120.0;
const double contactsColumnWidthDesktop = 140.0;
const double emailColumnWidthTablet = 180.0;
const double emailColumnWidthDesktop = 200.0;
const double joinDateColumnWidthTablet = 100.0;
const double joinDateColumnWidthDesktop = 120.0;
const double addressColumnWidthTablet = 150.0;
const double addressColumnWidthDesktop = 180.0;
const double totalAgentsColumnWidthTablet = 100.0;
const double totalAgentsColumnWidthDesktop = 100.0;
const double totalSalesColumnWidthTablet = 120.0;
const double totalSalesColumnWidthDesktop = 140.0;
const double defaultColumnWidthTablet = 100.0;
const double defaultColumnWidthDesktop = 120.0;

// Container configuration
const double webContainerWidthPercentage = 0.85; // 85% of screen width
const double webContainerHeightPercentage = 0.75; // 75% of screen height
const double minContainerWidth = 1200.0;
const double maxContainerWidth = 1600.0;
const double minContainerHeight = 500.0;
const double containerPadding = 20.0;
const double containerPaddingLeft = 30.0;
const double borderRadius = 12.0;
const double headerSpacing = 30.0;

const double titleFontSize = 22.0;
const FontWeight titleFontWeight = FontWeight.w600;

// Assets
const imageAssetpath = 'assets/images';
const iconAssetpath = 'assets/icons';
const launcherAssetpath = 'assets/launcher';
const String sortIconPath = 'assets/icons/column_sort.png';
const String userIconPath = 'assets/icons/agent_round.png';
const String eyeIconPath = 'assets/icons/eye.png';
const String filterIconPath = 'assets/icons/filter.png';
const String userIconTablePath = 'assets/icons/user.png';
const String searchIconPath = 'assets/icons/search.png';

// Responsive Helper Functions
// Agent Table
class ResponsiveSizes {
  // Icon sizes
  static double iconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return MediaQuery.of(context).size.width * 0.06; // ~45 for mobile
    } else if (screenWidth < tabletBreakpoint) {
      return MediaQuery.of(context).size.width * 0.045; // ~55 for tablet
    } else {
      return MediaQuery.of(context).size.width * 0.04; // ~55 for desktop
    }
  }

  // Button heights
  static double buttonHeight(BuildContext context) {
    return MediaQuery.of(context).size.height * 0.06; // ~50
  }

  // Font sizes
  static double largeFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return MediaQuery.of(context).size.width * 0.035; // ~25 for mobile
    } else {
      return MediaQuery.of(context).size.width * 0.025; // ~35 for desktop
    }
  }

  // Container widths
  static double filterButtonWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 500) {
      // Small mobile
      return screenWidth * 0.22; // ~110 for 500px width, ~88 for 400px
    } else if (screenWidth < mobileBreakpoint) {
      return screenWidth * 0.15; // ~107 for mobile
    } else {
      return screenWidth * 0.05; // ~107 for tablet/desktop
    }
  }

  static double searchFieldWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 500) {
      // Small mobile
      return screenWidth * 0.55; // ~275 for 500px width, ~220 for 400px
    } else if (screenWidth < mobileBreakpoint) {
      return screenWidth * 0.4; // ~285 for mobile
    } else {
      return screenWidth * 0.15; // ~223 for tablet/desktop
    }
  }

  static double dropdownWidth(BuildContext context) {
    return MediaQuery.of(context).size.width * 0.16; // ~240
  }

  static double statusChipWidth(BuildContext context) {
    return MediaQuery.of(context).size.width * 0.065; // ~96
  }

  static double comboBoxWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 500) {
      // Small mobile
      return screenWidth * 0.8; // Full width for small mobile
    } else if (screenWidth < mobileBreakpoint) {
      return screenWidth * 0.4; // 40% width for mobile
    } else if (screenWidth < tabletBreakpoint) {
      return screenWidth * 0.2; // 20% width for tablet
    } else {
      return 240.0; // Fixed width for desktop
    }
  }

  // For apply button in filter
  static double applyButtonWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 500) {
      // Small mobile
      return screenWidth * 0.8; // Full width for small mobile
    } else if (screenWidth < mobileBreakpoint) {
      return screenWidth * 0.3; // 30% width for mobile
    } else if (screenWidth < tabletBreakpoint) {
      return screenWidth * 0.15; // 15% width for tablet
    } else {
      return 136.0; // Same as combo boxes for desktop
    }
  }
}
