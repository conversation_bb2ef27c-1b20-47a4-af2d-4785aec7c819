import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:neorevv/src/presentation/screens/sales/sales_screen.dart';
import 'src/core/config/constants.dart';
import 'src/presentation/screens/auth/login_screen.dart';
import '/src/domain/repository/auth_repository.dart';
import 'src/presentation/screens/dashboard/dashboard_screen.dart';
import 'src/core/config/app_strings.dart';
import 'src/presentation/screens/sales/sales_review_doc_screen.dart';
import 'src/core/services/locator.dart';
import 'src/presentation/cubit/auth/cubit/auth_cubit.dart';
import 'src/core/theme/app_theme.dart';
import 'src/presentation/screens/broker/register_broker_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initializeDependencies();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => AuthCubit(locator<AuthRepository>())),
      ],
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: appName,
        theme: ThemeData(
          fontFamily: fontFamily,
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppTheme.primaryColor,
            brightness: Brightness.light,
          ),

          scaffoldBackgroundColor: AppTheme.scaffoldBgColor,
        ),
        home: SalesScreen(),
        routes: {
          '/dashboard': (context) => const DashboardScreen(),
          '/register-broker': (context) => RegisterBrokerScreen(),
          '/sale-review-doc': (context) => SalesReviewDocScreen(),
        },
      ),
    );
  }
}
