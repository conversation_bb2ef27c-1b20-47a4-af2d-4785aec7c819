import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/config/app_strings.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/config/responsive.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'dart:math' as math;

class TableCellData {
  final String text;
  final Widget? widget;
  final TextAlign? alignment;

  TableCellData({required this.text, this.widget, this.alignment});
}

class CustomDataTableWidget<T> extends HookWidget {
  final List<T> data;
  final String? title;
  final String? titleIcon;
  final String? searchHint;

  // Legacy filter system (for backward compatibility)
  final List<String> filter1Options;
  final List<String> filter2Options;
  final List<String> filter3Options;
  final String? filter1Label;
  final String? filter2Label;
  final String? filter3Label;
  final bool Function(T item, String?, String?, String?)? filterFn;

  // Dynamic filter configuration (new system)
  final List<String> filterColumnNames; // Column names to create filters for
  final Map<String, String Function(T)>? filterValueExtractors; // How to extract values for each filter column

  final String Function(T item) searchFn;
  final List<String> columnNames;
  final List<String Function(T)> cellBuilders;
  final List<Widget Function(BuildContext, T)>? actionBuilders;
  final Widget Function(BuildContext, T)? mobileCardBuilder;
  final void Function(String columnName, bool ascending)? onSort;

  // Empty state customization
  final String? emptyStateMessage;

  const CustomDataTableWidget({
    super.key,
    required this.data,
    this.title,
    this.titleIcon,
    this.searchHint,
    required this.searchFn,
    required this.columnNames,
    required this.cellBuilders,
    this.actionBuilders,
    this.mobileCardBuilder,
    this.onSort,
    // Legacy filter system
    this.filter1Options = const [],
    this.filter2Options = const [],
    this.filter3Options = const [],
    this.filter1Label,
    this.filter2Label,
    this.filter3Label,
    this.filterFn,
    // Dynamic filter system
    this.filterColumnNames = const [],
    this.filterValueExtractors,
    this.emptyStateMessage,
  });

  @override
  Widget build(BuildContext context) {
    final currentPage = useState(1);
    final itemsPerPage = useState(10);
    final selectedF1 = useState<String?>(null);
    final selectedF2 = useState<String?>(null);
    final selectedF3 = useState<String?>(null);
    final searchQuery = useState('');
    final showFilter = useState(false);
    final showTooltip = useState(false);
    final appliedF1 = useState<String?>(null);
    final appliedF2 = useState<String?>(null);
    final appliedF3 = useState<String?>(null);
    final sortColumn = useState<String>('');
    final sortAscending = useState<bool>(true);

    // Dynamic filter states
    final appliedFilters = useState<Map<String, String?>>({});

    void handleSort(String columnName) {
      if (onSort != null) {
        if (sortColumn.value == columnName) {
          sortAscending.value = !sortAscending.value;
        } else {
          sortColumn.value = columnName;
          sortAscending.value = true;
        }
        onSort!(columnName, sortAscending.value);
      }
    }

    List<T> getFilteredItems() {
      var filtered = data;

      if (searchQuery.value.isNotEmpty) {
        filtered = filtered
            .where(
              (item) => searchFn(
                item,
              ).toLowerCase().contains(searchQuery.value.toLowerCase()),
            )
            .toList();
      }

      // Apply legacy filters if filterFn is provided
      if (filterFn != null) {
        filtered = filtered
            .where(
              (item) => filterFn!(
                item,
                filter1Options.isNotEmpty ? appliedF1.value : null,
                filter2Options.isNotEmpty ? appliedF2.value : null,
                filter3Options.isNotEmpty ? appliedF3.value : null,
              ),
            )
            .toList();
      }

      // Apply dynamic filters if configured
      if (filterColumnNames.isNotEmpty && filterValueExtractors != null) {
        for (String columnName in filterColumnNames) {
          final appliedValue = appliedFilters.value[columnName];
          if (appliedValue != null && appliedValue.isNotEmpty) {
            final extractor = filterValueExtractors![columnName];
            if (extractor != null) {
              filtered = filtered
                  .where((item) => extractor(item) == appliedValue)
                  .toList();
            }
          }
        }
      }
      return filtered;
    }

    List<T> getPaginatedItems() {
      final filtered = getFilteredItems();
      final startIndex = (currentPage.value - 1) * itemsPerPage.value;
      final endIndex = (startIndex + itemsPerPage.value).clamp(
        0,
        filtered.length,
      );
      return filtered.sublist(startIndex, endIndex);
    }

    int getTotalPages() =>
        (getFilteredItems().length / itemsPerPage.value).ceil();

    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            Container(
              width: constraints.maxWidth,
              padding: const EdgeInsets.only(bottom: defaultPadding - 2),
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.black.withValues(alpha: 0.05),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (showFilter.value)
                      _buildFilterWidget(
                        context,
                        selectedF1,
                        selectedF2,
                        selectedF3,
                        showFilter,
                        currentPage,
                        showTooltip,
                        appliedF1,
                        appliedF2,
                        appliedF3,
                        appliedFilters,
                      ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(
                        defaultPadding * 1.5,
                        defaultPadding - 2,
                        defaultPadding * 1.5,
                        0,
                      ),
                      child: _buildHeader(
                        context,
                        searchQuery,
                        showFilter,
                        showTooltip,
                        appliedF1,
                        appliedF2,
                        appliedF3,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                      ),
                      child: _buildTable(
                        context,
                        getPaginatedItems(),
                        handleSort,
                        sortColumn,
                        sortAscending,
                      ),
                    ),
                    const SizedBox(height: defaultPadding),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                      ),
                      child: _buildPagination(
                        context,
                        getFilteredItems(),
                        currentPage,
                        itemsPerPage,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

 Widget _buildFilterWidget(
  BuildContext context,
  ValueNotifier<String?> selectedFilter1,
  ValueNotifier<String?> selectedFilter2,
  ValueNotifier<String?> selectedFilter3,
  ValueNotifier<bool> showFilter,
  ValueNotifier<int> currentPage,
  ValueNotifier<bool> showTooltip,
  ValueNotifier<String?> appliedFilter1,
  ValueNotifier<String?> appliedFilter2,
  ValueNotifier<String?> appliedFilter3,
  ValueNotifier<Map<String, String?>> appliedFilters,
) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(color: AppTheme.filterBgColor),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(filterBy, style: AppFonts.semiBoldTextStyle(18)),
            GestureDetector(
              onTap: () {
                showFilter.value = false;
              },
              child: const Icon(Icons.close, size: 20),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Make filter controls responsive with proper wrapping
        LayoutBuilder(
          builder: (context, constraints) {
            final isSmallScreen = constraints.maxWidth < 600;

            // Build dynamic filters
            List<Widget> filterWidgets = [];

            // Add legacy filters if they exist
            if (filter1Options.isNotEmpty && filter1Label != null) {
              filterWidgets.add(_buildDropdown(
                filter1Label!,
                selectedFilter1.value,
                filter1Options,
                (value) => selectedFilter1.value = value,
              ));
            }
            if (filter2Options.isNotEmpty && filter2Label != null) {
              filterWidgets.add(_buildDropdown(
                filter2Label!,
                selectedFilter2.value,
                filter2Options,
                (value) => selectedFilter2.value = value,
              ));
            }
            if (filter3Options.isNotEmpty && filter3Label != null) {
              filterWidgets.add(_buildDropdown(
                filter3Label!,
                selectedFilter3.value,
                filter3Options,
                (value) => selectedFilter3.value = value,
              ));
            }

            // Add dynamic filters
            for (String columnName in filterColumnNames) {
              final options = _getDynamicFilterOptions(columnName);
              if (options.isNotEmpty) {
                filterWidgets.add(_buildDropdown(
                  _getFilterLabel(columnName),
                  appliedFilters.value[columnName],
                  options,
                  (value) {
                    final newFilters = Map<String, String?>.from(appliedFilters.value);
                    newFilters[columnName] = value;
                    appliedFilters.value = newFilters;
                  },
                ));
              }
            }

            if (isSmallScreen) {
              // Stack filters vertically on small screens
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (int i = 0; i < filterWidgets.length; i++) ...[
                    SizedBox(
                      width: double.infinity,
                      child: filterWidgets[i],
                    ),
                    if (i < filterWidgets.length - 1) const SizedBox(height: 12),
                  ],
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    height: 40,
                    child: ElevatedButton(
                      onPressed: () {
                        // Apply legacy filters
                        appliedFilter1.value = selectedFilter1.value;
                        appliedFilter2.value = selectedFilter2.value;
                        appliedFilter3.value = selectedFilter3.value;
                        // Dynamic filters are already applied in real-time
                        currentPage.value = 1;
                        showTooltip.value = true;
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.roundIconColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: const Text(apply),
                    ),
                  ),
                ],
              );
            } else {
              // Use horizontal layout for larger screens with proper wrapping
              return Wrap(
                spacing: 12,
                runSpacing: 12,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  // Add all filter widgets (legacy + dynamic)
                  for (Widget filterWidget in filterWidgets)
                    SizedBox(
                      width: ResponsiveSizes.comboBoxWidth(context),
                      child: filterWidget,
                    ),
                  SizedBox(
                    width: ResponsiveSizes.applyButtonWidth(context),
                    height: 40,
                    child: ElevatedButton(
                      onPressed: () {
                        // Apply legacy filters
                        appliedFilter1.value = selectedFilter1.value;
                        appliedFilter2.value = selectedFilter2.value;
                        appliedFilter3.value = selectedFilter3.value;
                        // Dynamic filters are already applied in real-time
                        currentPage.value = 1;
                        showTooltip.value = true;
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.roundIconColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: const Text(apply),
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ],
    ),
  );
}
Widget _buildDropdown(
  String hint,
  String? value,
  List<String> items,
  Function(String?) onChanged,
) {
  return Container(
    height: 40,
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(20),
      border: Border.all(
        color: value != null
              ? AppTheme.selectedComboBoxBorder
              : AppTheme.comboBoxBorder,
        width: 1.0,
      ),
    ),
    child: DropdownButtonHideUnderline(
      child: ButtonTheme(
        alignedDropdown: true,
        child: DropdownButton<String>(
          value: value,
          hint: Padding(
            padding: const EdgeInsets.only(left: 12),
            child: Text(
              hint,
              style: AppFonts.regularTextStyle(14, color: AppTheme.black),
            ),
          ),
          isExpanded: true,
          dropdownColor: Colors.white,
          menuMaxHeight: 300,
          borderRadius: BorderRadius.circular(20),
          items: items.map((item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Padding(
                padding: const EdgeInsets.only(left: 12),
                child: Text(item, style: AppFonts.regularTextStyle(14)),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    ),
  );
}

  Widget _buildHeader(
    BuildContext context,
    ValueNotifier<String> searchQuery,
    ValueNotifier<bool> showFilter,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
  ) {
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (titleIcon != null) ...[
                SizedBox(width: 20, height: 20, child: Image.asset(titleIcon!)),
                const SizedBox(width: 8),
              ],
              Expanded(
                child: Text(title ?? '', style: AppFonts.semiBoldTextStyle(18)),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () {
                    showFilter.value = !showFilter.value;
                    // if (showFilter.value) {
                    //   // When opening filter, restore previously selected values
                    //   // selectedF1.value = appliedF1.value;
                    //   // selectedF2.value = appliedF2.value;
                    //   // selectedF3.value = appliedF3.value;
                    // }
                  },
                  child: Container(
                    constraints: BoxConstraints(
                      minWidth: ResponsiveSizes.filterButtonWidth(context),
                    ),
                    height: 40,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: showFilter.value
                          ? AppTheme.selectedComboBoxBorder
                          : AppTheme.searchbarBg,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: Image.asset(
                            '$iconAssetpath/filter.png',
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          "Filter",
                          style: AppFonts.regularTextStyle(
                            14,
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SizedBox(
                  height: 40,
                  child: TextField(
                    onChanged: (value) => searchQuery.value = value,
                    decoration: InputDecoration(
                      hintText: searchHint ?? 'Search',
                      hintStyle: AppFonts.regularTextStyle(
                        14,
                        color: AppTheme.tableDataFont,
                      ),
                      prefixIcon: Container(
                        height: 24,
                        width: 24,
                        padding: const EdgeInsets.only(
                          left: 8,
                          top: 8,
                          bottom: 8,
                        ),
                        child: Image.asset('$iconAssetpath/search.png'),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: AppTheme.searchbarBg,
                      contentPadding: const EdgeInsets.symmetric(vertical: 0),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }
    // For larger screens, use the original row layout
    return Row(
      children: [
        Row(
          children: [
            if (titleIcon != null) ...[
              SizedBox(width: 20, height: 20, child: Image.asset(titleIcon!)),
              const SizedBox(width: 8),
            ],
            Text(title ?? '', style: AppFonts.semiBoldTextStyle(22)),
          ],
        ),
        const Spacer(),
        Expanded(
          flex: 0,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () => showFilter.value = !showFilter.value,
                  child: Container(
                    constraints: BoxConstraints(
                      minWidth: _getFilterButtonMinWidth(context),
                      maxWidth: _getFilterButtonMaxWidth(context),
                    ),
                    height: 40,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: showFilter.value
                          ? AppTheme.selectedComboBoxBorder
                          : AppTheme.searchbarBg,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: Image.asset(
                            '$iconAssetpath/filter.png',
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          "Filter",
                          style: AppFonts.regularTextStyle(
                            14,
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                constraints: BoxConstraints(
                  minWidth: _getSearchFieldMinWidth(context),
                  maxWidth: _getSearchFieldMaxWidth(context),
                ),
                height: 40,
                child: TextField(
                  onChanged: (value) => searchQuery.value = value,
                  decoration: InputDecoration(
                    hintText: searchHint ?? 'Search',
                    hintStyle: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.tableDataFont,
                    ),
                    prefixIcon: Container(
                      height: 24,
                      width: 24,
                      padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
                      child: Image.asset('$iconAssetpath/search.png'),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: AppTheme.searchbarBg,
                    contentPadding: const EdgeInsets.symmetric(vertical: 0),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }



  Widget _buildTable(
    BuildContext context,
    List<T> items,
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    // Show empty state if no items
    if (items.isEmpty) {
      return _buildEmptyState(context);
    }

    if (Responsive.isMobile(context)) {
      return _buildMobileView(context, items);
    } else if (Responsive.isTablet(context)) {
      return _buildTabletTable(
        context,
        items,
        handleSort,
        sortColumn,
        sortAscending,
      );
    } else {
      return _buildDesktopTable(
        context,
        items,
        handleSort,
        sortColumn,
        sortAscending,
      );
    }
  }

  Widget _buildDesktopTable(
    BuildContext context,
    List<T> items,
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final ScrollController horizontalScrollController = ScrollController();

        final columns = columnNames
            .map(
              (name) => _dataColumn(
                name: name,
                allowSort: true,
                handleSort: handleSort,
                sortColumn: sortColumn.value,
                sortAscending: sortAscending.value,
              ),
            )
            .toList();

        if (actionBuilders != null && actionBuilders!.isNotEmpty) {
          columns.add(
            _dataColumn(
              name: 'Actions',
              allowSort: false,
              handleSort: handleSort,
              sortColumn: sortColumn.value,
              sortAscending: sortAscending.value,
            ),
          );
        }

        // Calculate minimum required width based on number of columns
        final int totalColumns = columnNames.length + (actionBuilders != null && actionBuilders!.isNotEmpty ? 1 : 0);
        final double minRequiredWidth = totalColumns * 120.0; // Minimum 120px per column
        final bool enableScroll = constraints.maxWidth < minRequiredWidth;

        Widget tableContent = SizedBox(
          width: enableScroll
              ? math.max(minRequiredWidth, 1200)
              : constraints.maxWidth,
          child: DataTable(
            columnSpacing: defaultPadding * 0.8,
            dataRowMinHeight: 40,
            dataRowMaxHeight: 50,
            horizontalMargin: 0,
            checkboxHorizontalMargin: 0,
            columns: columns,
            rows: items.map((item) => _buildDataRow(context, item)).toList(),
          ),
        );

        if (enableScroll) {
          return Scrollbar(
            controller: horizontalScrollController,
            thumbVisibility: true,
            child: SingleChildScrollView(
              controller: horizontalScrollController,
              scrollDirection: Axis.horizontal,
              child: tableContent,
            ),
          );
        } else {
          return tableContent;
        }
      },
    );
  }

  Widget _buildTabletTable(
    BuildContext context,
    List<T> items,
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final ScrollController horizontalScrollController = ScrollController();

        final columns = columnNames
            .map(
              (name) => _dataColumn(
                name: name,
                allowSort: true,
                handleSort: handleSort,
                sortColumn: sortColumn.value,
                sortAscending: sortAscending.value,
                fontSize: 12,
              ),
            )
            .toList();

        if (actionBuilders != null && actionBuilders!.isNotEmpty) {
          columns.add(
            _dataColumn(
              name: 'Actions',
              allowSort: false,
              handleSort: handleSort,
              sortColumn: sortColumn.value,
              sortAscending: sortAscending.value,
              fontSize: 12,
            ),
          );
        }

        // Calculate minimum required width for tablet based on number of columns
        final int totalColumns = columnNames.length + (actionBuilders != null && actionBuilders!.isNotEmpty ? 1 : 0);
        final double minRequiredWidth = totalColumns * 100.0; // Minimum 100px per column for tablet

        return Scrollbar(
          controller: horizontalScrollController,
          thumbVisibility: true, // Always show scrollbar for tablet
          child: SingleChildScrollView(
            controller: horizontalScrollController,
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: math.max(minRequiredWidth, math.max(constraints.maxWidth, 800)),
              child: DataTable(
                columnSpacing: defaultPadding * 0.6,
                dataRowMinHeight: 48,
                dataRowMaxHeight: 52,
                horizontalMargin: 0,
                checkboxHorizontalMargin: 0,
                columns: columns,
                rows: items.map((item) => _buildDataRow(context, item)).toList(),
              ),
            ),
          ),
        );
      },
    );
  }

  DataColumn _dataColumn({
    required String name,
    bool allowSort = true,
    required void Function(String) handleSort,
    required String sortColumn,
    required bool sortAscending,
    double fontSize = 14,
  }) {
    return DataColumn(
      label: Expanded(
        child: GestureDetector(
          onTap: allowSort && name != 'Actions' ? () => handleSort(name) : null,
          child: Container(
            alignment: Alignment.centerLeft,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Flexible(
                  child: Text(
                    name,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.left,
                    style: AppFonts.regularTextStyle(
                      fontSize,
                      color: AppTheme.tableHeaderFont,
                    ),
                  ),
                ),
                if (allowSort && name != 'Actions') ...[
                  const SizedBox(width: 4),
                  Image.asset(
                    '$iconAssetpath/column_sort.png',
                    height: 16,
                    width: 16,
                    color: sortColumn == name
                        ? AppTheme.primaryColor
                        : AppTheme.tableHeaderFont,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  DataRow _buildDataRow(BuildContext context, T item) {
    final cells = <DataCell>[];

    for (int i = 0; i < cellBuilders.length; i++) {
      cells.add(
        DataCell(
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              cellBuilders[i](item),
              style: AppFonts.regularTextStyle(14),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ),
      );
    }

    if (actionBuilders != null && actionBuilders!.isNotEmpty) {
      cells.add(
        DataCell(
          Container(
            width: 120, // Fixed width for action column to prevent cutting off
            alignment: Alignment.centerLeft,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: actionBuilders!
                  .map((builder) => builder(context, item))
                  .toList(),
            ),
          ),
        ),
      );
    }

    return DataRow(cells: cells);
  }

  Widget _buildMobileView(BuildContext context, List<T> items) {
    if (mobileCardBuilder == null) {
      return const SizedBox();
    }

    return Column(
      children: items.map((item) => mobileCardBuilder!(context, item)).toList(),
    );
  }

  Widget _buildPagination(
    BuildContext context,
    List<T> filteredItems,
    ValueNotifier<int> currentPage,
    ValueNotifier<int> itemsPerPage,
  ) {
    // Don't show pagination if no data
    if (filteredItems.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalPages = (filteredItems.length / itemsPerPage.value).ceil();

    // Always show pagination info, even with 1 page
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _showingDataText(
            context,
            currentPage.value,
            itemsPerPage.value,
            filteredItems,
          ),
          if (totalPages > 1) ...[
            const SizedBox(height: defaultPadding),
            _buildPaginationControls(context, currentPage, totalPages),
          ],
        ],
      );
    }

    return Row(
      children: [
        _showingDataText(
          context,
          currentPage.value,
          itemsPerPage.value,
          filteredItems,
        ),
        if (totalPages > 0) ...[
          const Spacer(),
          _buildPaginationControls(context, currentPage, totalPages),
        ],
      ],
    );
  }

  Widget _showingDataText(
    BuildContext context,
    int currentPage,
    int itemsPerPage,
    List<T> filteredItems,
  ) {
    final startIndex = (currentPage - 1) * itemsPerPage + 1;
    final endIndex = (currentPage * itemsPerPage).clamp(0, filteredItems.length);

    return Text(
      'Showing $startIndex to $endIndex of ${filteredItems.length} entries',
      style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
    );
  }

  Widget _buildPaginationControls(
    BuildContext context,
    ValueNotifier<int> currentPage,
    int totalPages,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _paginationButton(
          icon: Icons.chevron_left,
          onPressed: currentPage.value > 1 ? () => currentPage.value-- : null,
        ),
        ...List.generate(math.min(5, totalPages), (index) {
          final pageNum = index + 1;
          return _paginationButton(
            label: pageNum.toString(),
            isSelected: pageNum == currentPage.value,
            onPressed: () => currentPage.value = pageNum,
          );
        }),
        if (totalPages > 5) ...[
          _paginationButton(label: '...'),
          _paginationButton(
            label: totalPages.toString(),
            onPressed: () => currentPage.value = totalPages,
          ),
        ],
        _paginationButton(
          icon: Icons.chevron_right,
          onPressed: currentPage.value < totalPages ? () => currentPage.value++ : null,
        ),
      ],
    );
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
    VoidCallback? onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: Material(
        color: isSelected ? AppTheme.paginationActiveBg : Colors.transparent,
        borderRadius: BorderRadius.circular(5),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(5),
          child: Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: icon != null
                  ? Icon(
                      icon,
                      size: 16,
                      color: isSelected ? Colors.white : Colors.black,
                    )
                  : Text(
                      label!,
                      style: AppFonts.regularTextStyle(
                        12,
                        color: isSelected ? Colors.white : Colors.black,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }

  // Helper methods for responsive filter button sizing
  double _getFilterButtonMinWidth(BuildContext context) {
    if (Responsive.isSmallMobile(context)) {
      return ResponsiveSizes.filterButtonWidth(context);
    } else if (Responsive.isTablet(context)) {
      return 90.0;
    } else {
      // Desktop
      return 100.0; // Slightly larger for desktop
    }
  }

  double _getFilterButtonMaxWidth(BuildContext context) {
    if (Responsive.isSmallMobile(context)) {
      return ResponsiveSizes.filterButtonWidth(context);
    } else if (Responsive.isTablet(context)) {
      return 120.0;
    } else {
      // Desktop
      return 140.0; // Larger max width for desktop
    }
  }

  double _getSearchFieldMinWidth(BuildContext context) {
    if (Responsive.isSmallMobile(context)) {
      return ResponsiveSizes.searchFieldWidth(context);
    } else if (Responsive.isTablet(context)) {
      return 200.0;
    } else {
      // Desktop
      return 250.0; // Larger min width for desktop
    }
  }

  double _getSearchFieldMaxWidth(BuildContext context) {
    if (Responsive.isSmallMobile(context)) {
      return ResponsiveSizes.searchFieldWidth(context);
    } else if (Responsive.isTablet(context)) {
      return 300.0;
    } else {
      // Desktop
      return 400.0; // Larger max width for desktop
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(emptyStatePadding),
      child: Center(
        child: Text(
          emptyStateMessage ?? 'No Data Found',
          style: AppFonts.regularTextStyle(
            Responsive.isMobile(context) ? 16 : 18,
            color: AppTheme.primaryTextColor.withValues(alpha: 0.6),
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  // Helper method to generate dynamic filter options
  List<String> _getDynamicFilterOptions(String columnName) {
    if (filterValueExtractors == null || !filterValueExtractors!.containsKey(columnName)) {
      return [];
    }

    final extractor = filterValueExtractors![columnName]!;
    final uniqueValues = data.map(extractor).toSet().toList();
    uniqueValues.sort(); // Sort alphabetically
    return uniqueValues;
  }

  // Helper method to get filter label (use column name if not specified)
  String _getFilterLabel(String columnName) {
    return columnName; // You can customize this logic if needed
  }
}
