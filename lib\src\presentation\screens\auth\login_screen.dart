import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../shared/components/app_textfield.dart';
import '../../cubit/auth/cubit/auth_cubit.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/validators.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_fonts.dart';
import 'components/auth_background.dart';
import 'components/social_login_button.dart';
import 'components/custom_checkbox.dart';
import 'components/primary_button.dart';
import '../dashboard/dashboard_screen.dart';

class LoginScreen extends HookWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final emailFocusNode = FocusNode();
    final passwordFocusNode = FocusNode();
    final obscurePassword = useState(true);
    final rememberMeState = useState(false);
    final emailError = useState<String?>(null);
    final passwordError = useState<String?>(null);
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final isLoading = useState(false);

    final size = MediaQuery.of(context).size;
    return Scaffold(
      body: AuthBackground(
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: Responsive.isMobile(context) ? double.infinity : 900,
              minHeight: Responsive.isMobile(context)
                  ? size.height * 0.8
                  : size.height * 0.6,
            ),
            child: SingleChildScrollView(
              child: Card(
                color: Colors.transparent,
                elevation: Responsive.isMobile(context) ? 0 : 10,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    Responsive.isMobile(context) ? 0 : 5,
                  ),
                ),
                margin: EdgeInsets.symmetric(
                  horizontal: Responsive.isMobile(context) ? 0 : 40,
                  vertical: Responsive.isMobile(context) ? 0 : 20,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    Responsive.isMobile(context) ? 0 : 5,
                  ),
                  child: IntrinsicHeight(
                    child: Responsive.isMobile(context)
                        ? LoginRightPanel(
                            isMobile: true,
                            emailController: emailController,
                            passwordController: passwordController,
                            obscurePassword: obscurePassword,
                            rememberMeState: rememberMeState,
                            emailError: emailError,
                            passwordError: passwordError,
                            formKey: formKey,
                            emailFocusNode: emailFocusNode,
                            passwordFocusNode: passwordFocusNode,
                          )
                        : Row(
                            children: [
                              if (!Responsive.isSmallMobile(context))
                                Expanded(
                                  flex: Responsive.isTablet(context) ? 4 : 5,
                                  child: const LoginLeftPanel(),
                                ),
                              Expanded(
                                flex: Responsive.isTablet(context) ? 6 : 5,
                                child: ValueListenableBuilder(
                                  valueListenable: isLoading,
                                  builder: (context, _isLoading, child) {
                                    return LoginRightPanel(
                                      isMobile: false,
                                      emailController: emailController,
                                      passwordController: passwordController,
                                      obscurePassword: obscurePassword,
                                      rememberMeState: rememberMeState,
                                      emailError: emailError,
                                      passwordError: passwordError,
                                      formKey: formKey,
                                      emailFocusNode: emailFocusNode,
                                      passwordFocusNode: passwordFocusNode,
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class LoginLeftPanel extends StatelessWidget {
  const LoginLeftPanel({super.key});

  @override
  Widget build(BuildContext context) {
    bool isDesktop = Responsive.isDesktop(context);
    return Container(
      padding: const EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: AppTheme.loginBgColor.withValues(alpha: 0.65),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Title with icon
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                '$iconAssetpath/login_title_icon.png',
                height: isDesktop ? 56 : 46,
                width: isDesktop ? 56 : 46,
              ),
              const SizedBox(width: 12),
              Text(
                appName,
                textAlign: TextAlign.center,
                style: AppFonts.boldTextStyle(
                  isDesktop ? 45 : 35,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),

          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: AppFonts.normalTextStyle(
                20,
                color: Colors.white,
              ).copyWith(height: 1.5),
              children: [
                TextSpan(text: appDescriptionP1),
                TextSpan(
                  text: appDescriptionP2,
                  style: AppFonts.semiBoldTextStyle(20, color: Colors.white),
                ),
                TextSpan(text: appDescriptionP3),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LoginRightPanel extends StatelessWidget {
  final bool isMobile;
  final FocusNode emailFocusNode;
  final FocusNode passwordFocusNode;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final ValueNotifier<bool> obscurePassword;
  final ValueNotifier<bool> rememberMeState;
  final ValueNotifier<String?> emailError;
  final ValueNotifier<String?> passwordError;
  final GlobalKey<FormState> formKey;

  const LoginRightPanel({
    super.key,
    required this.isMobile,
    required this.emailController,
    required this.passwordController,
    required this.obscurePassword,
    required this.rememberMeState,
    required this.emailError,
    required this.passwordError,
    required this.formKey,
    required this.emailFocusNode,
    required this.passwordFocusNode,
  });

  bool _isFormValid() {
    final email = emailController.text.trim();
    final password = passwordController.text.trim();
    return InputValidators.validateEmail(email) == null &&
        InputValidators.validatePassword(password) == null;
  }

  Future<void> _handleLogin(BuildContext context) async {
    if (!formKey.currentState!.validate()) return;

    final payload = {
      "username": emailController.text.trim(),
      "password": passwordController.text.trim(),
    };

    final loginCubit = context.read<AuthCubit>();
    await loginCubit.login(payload);

    final state = loginCubit.state;

    if (state is AuthSuccess) {
      _navigateToDashboard(context);
    } else if (state is AuthError) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(state.error)));
    }
  }

  Future<void> _handleGoogleLogin(BuildContext context) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    try {
      await Future.delayed(const Duration(seconds: 1));
      if (context.mounted) {
        Navigator.of(context).pop();
        _navigateToDashboard(context);
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Google login failed: $e')));
      }
    }
  }

  void _navigateToDashboard(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (_) => const DashboardScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isMobileView = Responsive.isMobile(context);

    return Container(
      padding: EdgeInsets.all(
        isMobileView ? defaultPadding : defaultPadding * 2,
      ),
      margin: EdgeInsets.all(isMobileView ? defaultPadding * 2 : 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isMobileView ? 5 : 0),
      ),
      child: Form(
        key: formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (isMobileView) ...[
              Text(
                appName,
                style: AppFonts.boldTextStyle(
                  28,
                  color: AppTheme.roundIconColor,
                ),
              ),
              const SizedBox(height: defaultPadding * 2),
            ],
            Text(
              loginTitle,
              style: AppFonts.semiBoldTextStyle(
                24,
                color: AppTheme.primaryTextColor,
              ),
            ),
            const SizedBox(height: defaultPadding * 2),
            SocialLoginButton(
              icon: Image.asset(
                '$iconAssetpath/google.png',
                height: 20,
                width: 20,
              ),
              text: signInWithGmail,
              onPressed: () => _handleGoogleLogin(context),
            ),
            const SizedBox(height: defaultPadding * 1.8),
            Text(
              orContinueWith,
              style: AppFonts.regularTextStyle(
                14,
                color: AppTheme.orContinueWithColor,
              ),
            ),
            const SizedBox(height: defaultPadding * 1.8),

            // Email Field
            _buildEmailField(isMobileView),
            const SizedBox(height: defaultPadding),

            // Password Field
            _buildPasswordField(isMobileView),
            const SizedBox(height: 8),

            // Remember me + Forgot password
            _buildAuthOptionsRow(),
            const SizedBox(height: defaultPadding * 1.5),

            // Login Button
            _buildLoginBtn(context),
            const SizedBox(height: defaultPadding * 2),
          ],
        ),
      ),
    );
  }

  ValueListenableBuilder<String?> _buildEmailField(bool isMobileView) {
    return ValueListenableBuilder<String?>(
      valueListenable: emailError,
      builder: (_, errorText, __) {
        return AppTextField(
          controller: emailController,
          hintText: emailHint,
          errorText: errorText,
          validator: InputValidators.validateEmail,
          focusNode: emailFocusNode,
          isMobile: isMobileView,
        );
      },
    );
  }

  ValueListenableBuilder<String?> _buildPasswordField(bool isMobileView) {
    return ValueListenableBuilder<String?>(
      valueListenable: passwordError,
      builder: (_, errorText, __) {
        return ValueListenableBuilder<bool>(
          valueListenable: obscurePassword,
          builder: (_, isObscured, __) {
            return AppTextField(
              controller: passwordController,
              hintText: passwordHint,
              errorText: errorText,
              validator: InputValidators.validatePassword,
              isObscure: isObscured,
              showToggle: true,
              onToggleObscure: () =>
                  obscurePassword.value = !obscurePassword.value,
              focusNode: passwordFocusNode,
              isMobile: isMobileView,
            );
          },
        );
      },
    );
  }

  ValueListenableBuilder<bool> _buildAuthOptionsRow() {
    return ValueListenableBuilder<bool>(
      valueListenable: rememberMeState,
      builder: (_, isChecked, __) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomCheckbox(
              value: isChecked,
              onChanged: (v) => rememberMeState.value = v ?? false,
              text: rememberMe,
            ),
            GestureDetector(
              onTap: () {}, // TODO: Forgot password logic
              child: Text(
                forgotPassword,
                style: AppFonts.mediumTextStyle(
                  14,
                  color: AppTheme.roundIconColor,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  BlocConsumer<AuthCubit, AuthState> _buildLoginBtn(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (_, state) {
        if (state is AuthSuccess) _navigateToDashboard(context);
        if (state is AuthError) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.error)));
        }
      },
      builder: (_, state) {
        return AnimatedBuilder(
          animation: Listenable.merge([emailController, passwordController]),
          builder: (_, __) {
            final isLoading = state is AuthLoading;
            return PrimaryButton(
              text: isLoading ? loggingIn : loginButton,
              height: 45,
              borderRadius: 25,
              onPressed: (isLoading || !_isFormValid())
                  ? null
                  : () => _handleLogin(context),
            );
          },
        );
      },
    );
  }
}
