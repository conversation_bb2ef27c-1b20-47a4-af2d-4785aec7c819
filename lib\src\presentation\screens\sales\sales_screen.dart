import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/config/json_consts.dart';
import 'package:neorevv/src/domain/models/sales.dart';
import 'package:neorevv/src/presentation/shared/tables/CustomDataTableWidget.dart';
import 'package:neorevv/src/presentation/screens/dashboard/components/brokers_table.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../dashboard/components/header.dart';

class SalesScreen extends HookWidget {
  const SalesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final sortedSales = useState<List<Sales>>(salesListJson);
    final header = Header(selectedTab: salesTab);

    void handleSort(String columnName, bool ascending) {
      final sorted = List<Sales>.from(sortedSales.value);
      sorted.sort((a, b) {
        dynamic aValue, bValue;

        switch (columnName) {
          case salesTransactionIdColumnHeader:
            aValue = a.transactionId;
            bValue = b.transactionId;
            break;
          case salesAgentColumnHeader:
            aValue = a.agentName;
            bValue = b.agentName;
            break;
          case salesPropertyTypeColumnHeader:
            aValue = a.propertyType;
            bValue = b.propertyType;
            break;
          case salesPropertyAddressColumnHeader:
            aValue = a.propertyAddress;
            bValue = b.propertyAddress;
            break;
          case salesPropertyValueColumnHeader:
            aValue = a.propertyValue;
            bValue = b.propertyValue;
            break;
          case salesBuyerNameColumnHeader:
            aValue = a.buyerName;
            bValue = b.buyerName;
            break;
          case salesBuyerAddressColumnHeader:
            aValue = a.buyerAddress;
            bValue = b.buyerAddress;
            break;
          case salesListingDateColumnHeader:
            aValue = a.listingDate;
            bValue = b.listingDate;
            break;
          case salesDateColumnHeader:
            aValue = a.saleDate;
            bValue = b.saleDate;
            break;
          case salesAmountColumnHeader:
            aValue = a.salePrice;
            bValue = b.salePrice;
            break;
          case salesCommissionColumnHeader:
            aValue = a.commissionPercent;
            bValue = b.commissionPercent;
            break;
          case salesCommissionAmtColumnHeader:
            aValue = a.commissionAmount;
            bValue = b.commissionAmount;
            break;
          default:
            return 0;
        }

        final comparison = aValue is num && bValue is num
            ? aValue.compareTo(bValue)
            : aValue is DateTime && bValue is DateTime
            ? aValue.compareTo(bValue)
            : aValue.toString().compareTo(bValue.toString());
        return ascending ? comparison : -comparison;
      });
      sortedSales.value = sorted;
    }

    return Scaffold(
      drawer: header.mobileDrawer,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.fromLTRB(
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            Responsive.isMobile(context) ? 8 : defaultMargin,
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            0,
          ),
          child: Column(
            children: [
              header,
              const SizedBox(height: defaultPadding),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: defaultPadding),

                      //Customized table layout

                      CustomDataTableWidget<Sales>(
                        data: sortedSales.value,
                        title: salesTab,
                        titleIcon: userIconTablePath,
                        searchHint: searchHint,
                        searchFn: (sale) => sale.transactionId + sale.agentName,
                        // Dynamic filtering system
                        filterColumnNames: [
                          salesPropertyTypeColumnHeader,
                          salesAgentColumnHeader,
                          salesTransactionIdColumnHeader
                        ],
                        filterValueExtractors: {
                          salesPropertyTypeColumnHeader: (sale) => sale.propertyType,
                          salesAgentColumnHeader: (sale) => sale.agentName,
                          salesTransactionIdColumnHeader: (sale) => sale.transactionId,
                        },
                        columnNames: [
                          salesTransactionIdColumnHeader,
                          salesAgentColumnHeader,
                          salesPropertyTypeColumnHeader,
                          salesPropertyAddressColumnHeader,
                          salesPropertyValueColumnHeader,
                          salesBuyerNameColumnHeader,
                          salesBuyerAddressColumnHeader,
                          salesListingDateColumnHeader,
                          salesDateColumnHeader,
                          salesAmountColumnHeader,
                          salesCommissionColumnHeader,
                          salesCommissionAmtColumnHeader,
                        ],
                        cellBuilders: [
                          (sale) => sale.transactionId,
                          (sale) => sale.agentName,
                          (sale) => sale.propertyType,
                          (sale) => sale.propertyAddress,
                          (sale) => '₹${sale.propertyValue.toStringAsFixed(2)}',
                          (sale) => sale.buyerName,
                          (sale) => sale.buyerAddress,
                          (sale) => '${sale.listingDate.day}/${sale.listingDate.month}/${sale.listingDate.year}',
                          (sale) => '${sale.saleDate.day}/${sale.saleDate.month}/${sale.saleDate.year}',
                          (sale) => '₹${sale.salePrice.toStringAsFixed(2)}',
                          (sale) => '${sale.commissionPercent.toStringAsFixed(1)}%',
                          (sale) => '₹${sale.commissionAmount.toStringAsFixed(2)}',
                        ],
                        actionBuilders: [
                          (context, sale) => ActionButtonEye(
                            onPressed: () => _onSaleAction(context, sale),
                            isCompact: true,
                            isMobile: false,
                          ),
                        ],

                        mobileCardBuilder: (context, sale) =>
                            _buildMobileSaleCard(sale, context),
                        onSort: handleSort,
                        emptyStateMessage: noDataAvailable,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onSaleAction(BuildContext context, Sales sale) {
    // Navigate to sales detail or show action
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Action clicked for ${sale.transactionId}')),
    );
  }

  Widget _buildMobileSaleCard(Sales sale, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                sale.transactionId,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: sale.propertyType == "Lease"
                      ? Colors.green.withOpacity(0.1)
                      : Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  sale.propertyType,
                  style: TextStyle(
                    color: sale.propertyType == "Lease"
                        ? Colors.green
                        : Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('Agent: ${sale.agentName}'),
          Text('Property Address: ${sale.propertyAddress}'),
          Text('Buyer Name: ${sale.buyerName}'),
          Text('Listing Date: ${sale.listingDate.day}/${sale.listingDate.month}/${sale.listingDate.year}'),
          Text('Sale Date: ${sale.saleDate.day}/${sale.saleDate.month}/${sale.saleDate.year}'),
          Text('Buyer Address: ${sale.buyerAddress}'),
          Text('Amount: ₹${sale.salePrice.toStringAsFixed(2)}'),
          Text('Commission: ₹${sale.commissionPercent.toStringAsFixed(2)}'),
          Text('Commission Amt: ₹${sale.commissionAmount.toStringAsFixed(2)}'),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onSaleAction(context, sale),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
