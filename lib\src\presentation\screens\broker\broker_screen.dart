import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/config/app_strings.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/broker.dart';
import 'package:neorevv/src/core/config/json_consts.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/presentation/screens/auth/components/tables/broker_card_list.dart';
import 'package:neorevv/src/presentation/screens/auth/components/tables/broker_table_config.dart';
import 'package:neorevv/src/presentation/screens/auth/components/tables/table_container.dart';
import 'package:neorevv/src/presentation/screens/dashboard/components/header.dart';

class BrokersScreen extends HookWidget {
  /// Converts a Broker object to a table row map
  Map<String, dynamic> _brokerToTableRow(Broker broker) {
    return {
      brokerNameKey: broker.name,
      contactsKey: broker.contact,
      emailAddressKey: broker.email,
      joinDateKey: broker.joinDate,
      addressKey: broker.address,
      totalAgentsKey: broker.agents.length,
      totalSalesRevenueKey: broker.totalSalesRevenue,
    };
  }

  BrokersScreen({super.key});
  // TODO: STATE_MANAGEMENT - Consider moving to state management solution (Provider/Bloc)
  // String searchQuery = '';
  // List<Broker> filteredBrokers = BrokerConstants.brokers;
  @override
  Widget build(BuildContext context) {
    final filteredBrokersState = useState<List<Broker>>(brokersListJson);
    final isWeb = MediaQuery.of(context).size.width > 800;

    // Use same horizontal padding as TableContainer (assumed 32.0, update if TableContainer uses a different value)
    const double tableHorizontalPadding = 32.0;
    return Scaffold(
      body: isWeb
          ? Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: !isWeb ? 8 : webLayoutmargin,
                    vertical: !isWeb ? 8 : defaultMargin,
                  ),
                  child: Header(selectedTab: brokersTab),
                ),
                Expanded(
                  child: _buildBrokerTable(context, filteredBrokersState),
                ),
              ],
            )
          : _buildMobileBrokerView(filteredBrokersState),
    );
  }

  Widget _buildBrokerTable(
    BuildContext context,
    ValueNotifier<List<Broker>> filteredBrokersState,
  ) {
    final columns = [
      const TableColumn(
        header: brokerScreenColumnHeader,
        sortable: true,
        alignment: TextAlign.left,
      ),
      const TableColumn(header: contactsColumnHeader, sortable: true),
      const TableColumn(header: emailAddressColumnHeader, sortable: true),
      const TableColumn(header: joinDateColumnHeader, sortable: true),
      const TableColumn(
        header: addressColumnHeader,
        sortable: true,
        alignment: TextAlign.left,
      ),
      const TableColumn(
        header: totalAgentsColumnHeader,
        sortable: true,
        alignment: TextAlign.left,
      ),
      const TableColumn(
        header: totalSalesVolumeKeyHeader,
        sortable: true,
        alignment: TextAlign.left,
      ),
    ];

    final actions = [
      TableAction(
        icon: Icons.visibility_outlined,
        tooltip: actionsColumnHeader,
        color: AppTheme.primaryBlueColor,
        onPressed: (rowIndex, rowData) =>
            _onBrokerAction(context, rowIndex, rowData),
        customBuilder: (rowIndex, rowData) {
          return Container(
            width: actionIconContainerSize,
            height: actionIconContainerSize,
            decoration: const BoxDecoration(
              color: AppTheme.userIconBackgroundColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Image.asset(
                eyeIconPath,
                width: sortIconSize,
                height: sortIconSize,
                fit: BoxFit.contain,
              ),
            ),
          );
        },
      ),
    ];

    return TableContainer(
      title: TableContainerTitle(
        text: brokersTitle,
        customIcon: Image.asset(
          userIconTablePath,
          width: titleIconSize,
          height: titleIconSize,
          color: AppTheme.titleIconColor,
        ),
      ),
      filterOptions: _buildFilterOptions(),
      columns: columns,
      data: filteredBrokersState.value
          .where((broker) => broker.name.isNotEmpty)
          .map(_brokerToTableRow)
          .toList(),
      actions: actions,
      itemsPerPage: defaultItemsPerPage,
      emptyMessage: noBrokersFound,
      showPagination: true,
      onSort: (columnKey, ascending) {
        filteredBrokersState.value = _sortBrokers(
          filteredBrokersState.value,
          columnKey,
          ascending,
        );
      },
    );
  }

  // TODO: MOBILE_UI - Add pull-to-refresh functionality
  // TODO: MOBILE_UI - Add floating action button for quick actions
  Widget _buildMobileBrokerView(
    ValueNotifier<List<Broker>> filteredBrokersState,
  ) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Padding(
        padding: const EdgeInsets.all(mobileScreenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title section
            _buildTitleSection(),
            const SizedBox(height: 16),

            // Filter and Search section
            _buildMobileFilterSection(),
            const SizedBox(height: 16),

            // Broker cards list
            Expanded(
              child: BrokerCardList(brokers: filteredBrokersState.value),
            ),
          ],
        ),
      ),
    );
  }

  /// TODO: UI_COMPONENTS - Extract title section to reusable widget
  Widget _buildTitleSection() {
    return Row(
      children: [
        Image.asset(
          userIconTablePath,
          width: titleIconSize,
          height: titleIconSize,
          color: AppTheme.titleIconColor,
        ),
        const SizedBox(width: 8),
        Text(
          brokersTitle,
          style: const TextStyle(
            fontSize: titleFontSize,
            fontWeight: titleFontWeight,
            fontFamily: fontFamily,
            color: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }

  // TODO: UI_COMPONENTS - Extract filter and search to separate widgets
  // TODO: FILTER_FUNCTIONALITY - Implement actual filter logic
  List<Widget> _buildFilterOptions() {
    return [_buildFilterButton(), _buildSearchField()];
  }

  /// TODO: UI_COMPONENTS - Convert to reusable widget component
  Widget _buildFilterButton() {
    return InkWell(
      onTap: () {
        // TODO: FILTER_FUNCTIONALITY - Implement filter dialog/dropdown
      },
      child: Container(
        height: filterButtonHeight,
        padding: const EdgeInsets.symmetric(
          horizontal: filterButtonPaddingHorizontal,
          vertical: filterButtonPaddingVertical,
        ),
        decoration: BoxDecoration(
          color: AppTheme.buttonBackgroundColor,
          borderRadius: BorderRadius.circular(searchFieldBorderRadius),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              filterIconPath,
              width: filterIconSize,
              height: filterIconSize,
              color: AppTheme.filterIconColor,
            ),
            const SizedBox(width: filterIconSpacing),
            const Text(
              filter,
              style: TextStyle(
                color: AppTheme.filterTextColor,
                fontSize: filterTextSize,
                fontFamily: fontFamily,
                fontWeight: filterTextWeight,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// TODO: UI_COMPONENTS - Convert to reusable widget component
  Widget _buildSearchField({bool isExpanded = false}) {
    return Container(
      height: filterButtonHeight,
      constraints: isExpanded
          ? null
          : const BoxConstraints(
              minWidth: searchFieldMinWidth,
              maxWidth: searchFieldMaxWidth,
            ),
      decoration: BoxDecoration(
        color: AppTheme.buttonBackgroundColor,
        borderRadius: BorderRadius.circular(searchFieldBorderRadius),
      ),
      child: TextField(
        onChanged: _onSearchChanged,
        style: const TextStyle(
          fontFamily: fontFamily,
          fontSize: searchTextSize,
          color: AppTheme.searchTextColor,
        ),
        decoration: InputDecoration(
          hintText: searchHint,
          hintStyle: const TextStyle(
            color: AppTheme.searchHintColor,
            fontSize: searchHintTextSize,
            fontFamily: fontFamily,
            fontWeight: searchTextWeight,
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.only(
              left: searchIconPaddingLeft,
              top: searchIconPaddingVertical,
              bottom: searchIconPaddingVertical,
            ),
            child: Image.asset(
              searchIconPath,
              width: searchIconSize,
              height: searchIconSize,
              color: AppTheme.searchIconColor,
            ),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: searchContentPaddingHorizontal,
            vertical: searchContentPaddingVertical,
          ),
          isDense: true,
        ),
      ),
    );
  }

  /// TODO: UI_COMPONENTS - Optimize mobile layout for better UX
  Widget _buildMobileFilterSection() {
    return Row(
      children: [
        _buildFilterButton(),
        const SizedBox(width: 12),
        Expanded(child: _buildSearchField(isExpanded: true)),
      ],
    );
  }

  void _onSearchChanged(String query) {
    // To enable search, you can update filteredBrokersState.value here
  }

  /// TODO: OPTIMIZATION - Extract search logic for reusability
  List<Broker> _filterBrokers(String query) {
    if (query.isEmpty) {
      return brokersListJson;
    }

    final lowercaseQuery = query.toLowerCase();
    return brokersListJson.where((broker) {
      return broker.name.toLowerCase().contains(lowercaseQuery) ||
          broker.email.toLowerCase().contains(lowercaseQuery) ||
          (broker.address?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  // TODO: NAVIGATION - Implement proper broker detail navigation
  // TODO: UI_FEEDBACK - Replace SnackBar with proper action (navigate to detail page)
  void _onBrokerAction(
    BuildContext context,
    int rowIndex,
    Map<String, dynamic> rowData,
  ) {
    final brokerName = rowData[brokerNameKey] ?? '';
    _showActionFeedback(context, brokerName);
  }

  /// TODO: UI_FEEDBACK - Extract feedback logic for reusability
  void _showActionFeedback(BuildContext context, String brokerName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${actionClickedFor}$brokerName'),
        duration: const Duration(seconds: snackBarDurationSeconds),
      ),
    );
  }

  // TODO: SORT_FUNCTIONALITY - Add sort indicators in column headers
  // TODO: SORT_FUNCTIONALITY - Add multi-column sorting capability
  // TODO: OPTIMIZATION - Extract sort logic to separate service class
  void _onSort(String columnKey, bool ascending) {
    // Now handled by useState in TableContainer's onSort
  }

  /// TODO: OPTIMIZATION - Extract sorting logic for reusability and testing
  List<Broker> _sortBrokers(
    List<Broker> brokers,
    String columnKey,
    bool ascending,
  ) {
    final sortedBrokers = List<Broker>.from(brokers);

    sortedBrokers.sort((a, b) {
      final comparison = _compareValues(a, b, columnKey);
      return ascending ? comparison : -comparison;
    });

    return sortedBrokers;
  }

  /// TODO: OPTIMIZATION - Add type-specific comparison (dates, numbers, strings)
  int _compareValues(Broker a, Broker b, String columnKey) {
    dynamic aValue = _getValueByColumn(a, columnKey);
    dynamic bValue = _getValueByColumn(b, columnKey);

    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return -1;
    if (bValue == null) return 1;

    return aValue.toString().compareTo(bValue.toString());
  }

  /// TODO: OPTIMIZATION - Use reflection or map-based approach for better maintainability
  dynamic _getValueByColumn(Broker broker, String columnKey) {
    switch (columnKey) {
      case brokerScreenColumnHeader:
        return broker.name;
      case contactsColumnHeader:
        return broker.contact;
      case emailAddressColumnHeader:
        return broker.email;
      case joinDateColumnHeader:
        return broker.joinDate;
      case addressColumnHeader:
        return broker.address;
      case totalAgentsColumnHeader:
        return broker.agents.length;
      case totalSalesVolumeKeyHeader:
        return broker.totalSalesRevenue;
      default:
        return '';
    }
  }

  // TODO: DATA_CONVERSION - Add data validation and error handling
  // TODO: OPTIMIZATION - Cache converted data to avoid repeated conversions
}
