import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/presentation/shared/components/breadcrumb_navigation.dart';
import 'package:neorevv/src/presentation/screens/agent/agents_screen.dart';
import 'package:neorevv/src/presentation/screens/dashboard/components/header.dart';
import 'package:neorevv/src/presentation/screens/sales/sales_review_doc_screen.dart';
import 'core/theme/app_theme.dart';
import 'core/config/constants.dart';
import 'core/theme/app_fonts.dart';
import 'core/config/app_strings.dart';
import 'core/config/app_strings.dart' as AppStrings;
import 'presentation/screens/dashboard/components/dashboard_content.dart';

class MainLayoutScreen extends HookWidget {
  const MainLayoutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final selectedTabIndex = useState<int>(0);

    // Map<String, Widget> tabs = [
    //   dashboardTab,
    //   brokersTab,
    //   agentsTab,
    //   salesTab,
    //   commissionTab,
    //   reportsTab,
    // ];

    final tabs = [
      {'title': dashboardTab, 'content': DashboardContent()},
      {
        'title': brokersTab,
        'content': const Scaffold(body: Center(child: Text('Brokers Tab'))),
      },
      {'title': agentsTab, 'content': const AgentsScreen()},
      {'title': salesTab, 'content': SalesReviewDocScreen()},
      {
        'title': commissionTab,
        'content': const Scaffold(body: Center(child: Text('Commission Tab'))),
      },
      {
        'title': reportsTab,
        'content': const Scaffold(body: Center(child: Text('Reports Tab'))),
      },
    ];
    void onTabSelected(int index) {
      selectedTabIndex.value = index;
    }

    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      body: Column(
        children: [
          // Fixed Header
          Container(
            margin: const EdgeInsets.symmetric(
              horizontal: defaultPadding,
              vertical: defaultPadding,
            ),
            height: 60,
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Row(
                children: [
                  // Header(selectedTab: selectedTab.value),

                  // Navigation Items
                  ...tabs.asMap().entries.map((entry) {
                    final index = entry.key;
                    final tab = entry.value;
                    return _buildNavItem(
                      tab['title'] as String,
                      selectedTabIndex.value == index,
                      () => onTabSelected(index),
                    );
                  }),

                  const Spacer(),

                  // Right side icons and user
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.notifications_outlined),
                        onPressed: () {},
                      ),
                      IconButton(
                        icon: const Icon(Icons.settings_outlined),
                        onPressed: () {},
                      ),
                      const SizedBox(width: 8),
                      Row(
                        children: [
                          const CircleAvatar(
                            radius: 16,
                            backgroundColor: AppTheme.primaryColor,
                            child: Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Nabii',
                                style: AppFonts.mediumTextStyle(
                                  14,
                                  color: AppTheme.primaryTextColor,
                                ),
                              ),
                              Text(
                                'Platform Owner',
                                style: AppFonts.regularTextStyle(
                                  12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                          const Icon(Icons.keyboard_arrow_down),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Fixed Breadcrumb
          BreadCrumbNavigation(
            hierarchyPath: [
              AppStrings.dashboardAdmin,
              tabs[selectedTabIndex.value]['title'] as String,
            ],
            onNavigate: (int navigationIndex) {},
          ),
          // Container(
          //   color: Colors.white,
          //   child: Padding(
          //     padding: const EdgeInsets.symmetric(
          //       horizontal: defaultPadding,
          //       vertical: 8,
          //     ),
          //     child: Row(
          //       children: [
          //         const Icon(Icons.home, size: 16, color: Colors.grey),
          //         const SizedBox(width: 4),
          //         Text(
          //           'Dashboard',
          //           style: AppFonts.regularTextStyle(14, color: Colors.grey),
          //         ),
          //         const Icon(Icons.chevron_right, size: 16, color: Colors.grey),
          //         Text(
          //           tabs[selectedTabIndex.value]['title'] as String,
          //           style: AppFonts.regularTextStyle(
          //             14,
          //             color: AppTheme.primaryColor,
          //           ),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),

          // Dynamic Content Area
          Expanded(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.1, 0),
                      end: Offset.zero,
                    ).animate(animation),
                    child: child,
                  ),
                );
              },
              child: Container(
                key: ValueKey(selectedTabIndex.value),
                child: tabs[selectedTabIndex.value]['content'] as Widget,
              ),
            ),
          ),

          // Fixed Footer
          const Footer(),
        ],
      ),
    );
  }

  Widget _buildNavItem(String title, bool isActive, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title,
                style: AppFonts.mediumTextStyle(
                  14,
                  color: isActive
                      ? AppTheme.primaryColor
                      : AppTheme.primaryTextColor,
                ),
              ),
              if (isActive)
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  height: 2,
                  width: 40,
                  color: AppTheme.primaryColor,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
