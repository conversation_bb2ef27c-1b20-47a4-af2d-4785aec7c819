import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/config/constants.dart';
import 'components/header.dart';
import 'components/dashboard_content.dart';

import '../../../core/theme/app_theme.dart';

class DashboardScreen extends HookWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final header = Header(selectedTab: dashboardTab);
    final bool isTablet = Responsive.isTablet(context);
    final bool isMobile = Responsive.isMobile(context);
    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      drawer: header.mobileDrawer,
      body: SafeArea(
        child: SingleChildScrollView(
          primary: false,
          padding: EdgeInsets.symmetric(
            horizontal: isMobile ? 8 : webLayoutmargin,
            vertical: isMobile ? 8 : defaultMargin,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(right: isTablet ? defaultMargin : 0),
                child: header,
              ),
              SizedBox(height: defaultPadding / 2),
              _welcomeUser(),
              SizedBox(height: defaultPadding / 2),
              DashboardContent(),
              const SizedBox(height: defaultPadding),
              const Footer(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _welcomeUser() {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        textAlign: TextAlign.left,
        text: TextSpan(
          text: welcomeLabel,
          style: AppFonts.regularTextStyle(
            22,
            color: AppTheme.primaryTextColor.withOpacity(0.7),
          ),
          children: [
            TextSpan(
              text: 'Nabil',
              style: AppFonts.semiBoldTextStyle(
                22,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
