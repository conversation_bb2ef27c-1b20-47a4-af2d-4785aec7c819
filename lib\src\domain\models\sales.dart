class Sales {
  final String transactionId;
  final String agentName;
  final String propertyType;
  final String propertyAddress;
  final double propertyValue;
  final String buyerName;
  final String buyerAddress;
  final DateTime listingDate;
  final DateTime saleDate;
  final double salePrice;
  final double commissionPercent;
  final double commissionAmount;

  Sales({
    required this.transactionId,
    required this.agentName,
    required this.propertyType,
    required this.propertyAddress,
    required this.propertyValue,
    required this.buyerName,
    required this.buyerAddress,
    required this.listingDate,
    required this.saleDate,
    required this.salePrice,
    required this.commissionPercent,
    required this.commissionAmount,
  });

  factory Sales.fromJson(Map<String, dynamic> json) {
    return Sales(
      transactionId: json['transactionId'],
      agentName: json['agentName'],
      propertyType: json['propertyType'],
      propertyAddress: json['propertyAddress'],
      propertyValue: (json['propertyValue'] as num).toDouble(),
      buyerName: json['buyerName'],
      buyerAddress: json['buyerAddress'],
      listingDate: DateTime.parse(json['listingDate']),
      saleDate: DateTime.parse(json['saleDate']),
      salePrice: (json['salePrice'] as num).toDouble(),
      commissionPercent: (json['commissionPercent'] as num).toDouble(),
      commissionAmount: (json['commissionAmount'] as num).toDouble(),
    );
  }
}
